#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信群消息监控与Coze智能回复系统
功能：监控微信群消息，调用Coze API进行智能回复，同步数据到飞书
"""

import requests
import json
import time
import datetime
import re
import base64
import logging
import urllib3
import subprocess

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置日志 - 只显示ERROR级别，保持输出干净
logging.basicConfig(level=logging.ERROR, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 设置urllib3日志级别为WARNING，去除HTTP连接的DEBUG信息
logging.getLogger("urllib3.connectionpool").setLevel(logging.WARNING)

# 配置信息
WECHAT_RPC_HOST = "http://127.0.0.1:50007"
WECHAT_PID = "15796"
COZE_TOKEN = "pat_iIRA1rSJkqdW1jW4NtvDVwQauDJEzGfur2QkYaaXR3kbO0Q5jZKQr7mGfirjvDMi"
COZE_BOT_ID = "7540166794171301940"

# 飞书配置信息
FEISHU_APP_ID = "cli_a828491ea031d013"
FEISHU_APP_SECRET = "eJsXnjJJVZh9K1XTnR7JbgxBzGbDMlRT"
FEISHU_APP_TOKEN = "TtULb7pBiaGRMgs4dfac4aLAnId"
FEISHU_TABLE_ID = "tblzpzFoRcDR3PC3"
FEISHU_BASE_URL = "https://open.feishu.cn/open-apis"

def execute_curl(method, url, headers=None, data=None):
    """执行curl命令 - 必须使用此方法避免网络问题"""
    cmd = ['curl', '-X', method, url, '--insecure', '--silent']

    if headers:
        for k, v in headers.items():
            cmd.extend(['-H', f'{k}: {v}'])
    if data:
        cmd.extend(['-d', data])

    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30, encoding='utf-8', errors='ignore')
        return json.loads(result.stdout)
    except Exception as e:
        logger.error(f"执行curl命令失败: {e}")
        return {"code": -1, "msg": str(e)}

def get_feishu_access_token():
    """获取飞书访问令牌"""
    try:
        url = f"{FEISHU_BASE_URL}/auth/v3/tenant_access_token/internal"
        data = json.dumps({
            "app_id": FEISHU_APP_ID,
            "app_secret": FEISHU_APP_SECRET
        })
        headers = {'Content-Type': 'application/json'}

        result = execute_curl('POST', url, headers, data)
        if result.get('code') == 0:
            return result['tenant_access_token']
        else:
            logger.error(f"获取飞书访问令牌失败: {result}")
            return None
    except Exception as e:
        logger.error(f"获取飞书访问令牌异常: {e}")
        return None

def parse_datetime_to_timestamp(datetime_str):
    """将日期时间字符串转换为毫秒时间戳"""
    try:
        # 解析格式: "2025-08-22 09:40:30"
        dt = datetime.datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
        return int(dt.timestamp() * 1000)  # 转换为毫秒时间戳
    except Exception as e:
        logger.error(f"时间格式转换失败: {e}")
        # 如果转换失败，使用当前时间
        return int(datetime.datetime.now().timestamp() * 1000)

def upload_message_to_feishu(message_data):
    """将消息数据上传到飞书多维表格"""
    try:
        token = get_feishu_access_token()
        if not token:
            logger.error("无法获取飞书访问令牌")
            return False

        # 转换时间格式
        send_time = message_data.get("发送时间", "")
        timestamp = parse_datetime_to_timestamp(send_time)

        # 准备飞书表格字段数据
        fields = {
            "发送时间": timestamp,  # 毫秒时间戳
            "发送人": message_data.get("发送人", ""),
            "群聊名称": message_data.get("群聊名称", ""),
            "消息内容": message_data.get("消息内容", ""),
            "消息类型": message_data.get("消息类型", ""),
            "消息ID": message_data.get("消息ID", ""),
            "被引用消息ID": message_data.get("被引用消息ID", "")
        }

        # API调用
        url = f"{FEISHU_BASE_URL}/bitable/v1/apps/{FEISHU_APP_TOKEN}/tables/{FEISHU_TABLE_ID}/records"
        data = json.dumps({"fields": fields})
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }

        result = execute_curl('POST', url, headers, data)

        if result.get('code') == 0:
            record_id = result['data']['record']['record_id']
            logger.info(f"✅ 成功上传到飞书: {record_id}")
            return True
        else:
            logger.error(f"上传到飞书失败: {result.get('msg')}")
            return False

    except Exception as e:
        logger.error(f"上传到飞书异常: {e}")
        return False

class WeChatCozeBot:
    def __init__(self):
        self.last_msg_id = 0
        self.contacts_cache = {}
        self.db_handle = None
        self.is_first_run = True  # 标记是否是第一次运行

    def get_db_handle(self):
        """获取数据库句柄"""
        try:
            response = requests.post(
                f"{WECHAT_RPC_HOST}/api/db/getDBInfo",
                headers={"X-WeChat-PID": WECHAT_PID},
                json={}
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("Code") == 1 and data.get("Data"):
                    # 查找MSG0.db的句柄
                    for db_info in data["Data"]:
                        if "MSG0.db" in db_info.get("databaseName", ""):
                            self.db_handle = str(db_info["handle"])
                            logger.info(f"获取到MSG0.db句柄: {self.db_handle}")
                            return True

                    logger.error("未找到MSG0.db数据库")
                    return False
                else:
                    logger.error(f"获取数据库信息失败: {data}")
                    return False
            else:
                logger.error(f"请求失败: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"获取数据库句柄异常: {e}")
            return False

    def load_contacts(self):
        """加载联系人信息到缓存"""
        try:
            # 获取MicroMsg.db句柄
            response = requests.post(
                f"{WECHAT_RPC_HOST}/api/db/getDBInfo",
                headers={"X-WeChat-PID": WECHAT_PID},
                json={}
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("Code") == 1 and data.get("Data"):
                    micromsg_handle = None
                    for db_info in data["Data"]:
                        if "MicroMsg.db" in db_info.get("databaseName", ""):
                            micromsg_handle = str(db_info["handle"])
                            break

                    if not micromsg_handle:
                        logger.warning("未找到MicroMsg.db，使用基础缓存")
                        return

                    # 查询联系人信息
                    sql = "SELECT UserName, NickName, Remark FROM Contact LIMIT 1000;"
                    response = requests.post(
                        f"{WECHAT_RPC_HOST}/api/db/execSql",
                        headers={"X-WeChat-PID": WECHAT_PID},
                        json={"dbHandle": micromsg_handle, "sql": sql}
                    )

                    if response.status_code == 200:
                        result = response.json()
                        if result.get("Code") == 1 and result.get("Data"):
                            for contact in result["Data"]:
                                username = contact[0]
                                nickname = contact[1] if contact[1] else ""
                                remark = contact[2] if contact[2] else ""

                                # 优先使用备注，其次昵称
                                display_name = remark if remark else nickname if nickname else username
                                self.contacts_cache[username] = display_name

                            logger.info(f"加载了 {len(self.contacts_cache)} 个联系人")
                        else:
                            logger.warning("联系人查询结果为空")
                    else:
                        logger.warning("联系人查询请求失败")

        except Exception as e:
            logger.warning(f"加载联系人异常: {e}")

    def get_contact_name(self, wxid):
        """获取联系人显示名称"""
        if wxid in self.contacts_cache:
            return self.contacts_cache[wxid]

        # 如果缓存中没有，返回微信ID
        return wxid

    def init_monitoring(self):
        """初始化监控起点 - 设置为当前最新消息ID，只监控实时消息"""
        try:
            sql = "SELECT MAX(localId) FROM MSG WHERE StrTalker LIKE '%@chatroom';"
            response = requests.post(
                f"{WECHAT_RPC_HOST}/api/db/execSql",
                headers={"X-WeChat-PID": WECHAT_PID},
                json={"dbHandle": self.db_handle, "sql": sql}
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("Code") == 1 and result.get("Data") and result["Data"][0][0] is not None:
                    try:
                        # 设置为当前最新消息ID，这样只会监控之后的新消息
                        max_id = result["Data"][0][0]
                        if isinstance(max_id, str) and max_id.isdigit():
                            self.last_msg_id = int(max_id)
                        elif isinstance(max_id, (int, float)):
                            self.last_msg_id = int(max_id)
                        else:
                            # 如果无法解析，设置为0
                            self.last_msg_id = 0
                            logger.warning(f"无法解析最大消息ID: {max_id}，从0开始监控")


                    except (ValueError, TypeError) as e:
                        self.last_msg_id = 0
                        logger.warning(f"解析最大消息ID失败: {e}，从0开始监控")
                else:
                    self.last_msg_id = 0
                    logger.info("未找到历史消息，从0开始监控")
            else:
                logger.error("初始化监控起点失败")
                self.last_msg_id = 0

        except Exception as e:
            logger.error(f"初始化监控起点异常: {e}")
            self.last_msg_id = 0

    def send_wechat_message(self, wxid, message):
        """发送微信消息"""
        try:
            response = requests.post(
                f"{WECHAT_RPC_HOST}/api/msg/sendTextMsg",
                headers={"X-WeChat-PID": WECHAT_PID},
                json={"wxid": wxid, "msg": message}
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("Code") == 1:
                    logger.info(f"消息发送成功到 {wxid}: {message}")
                    return True
                else:
                    logger.error(f"消息发送失败: {result}")
                    return False
            else:
                logger.error(f"发送消息请求失败: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"发送消息异常: {e}")
            return False

    def preprocess_message(self, content):
        """预处理消息内容，移除@机器人的部分"""
        # 移除@机器人的触发词，只保留实际消息内容
        trigger_word = "@我来收集总结你们的聊天(bot)"

        if trigger_word in content:
            # 移除触发词，保留后面的内容
            processed = content.replace(trigger_word, "").strip()

            # 如果移除触发词后没有内容，返回默认提示
            if not processed:
                return "请总结最近的聊天内容"

            return processed

        # 如果没有触发词，返回原内容（理论上不应该到这里，因为已经通过should_reply筛选）
        return content

    def parse_quote_info(self, compress_content, sub_type):
        """解析引用消息信息 - 提取MsgSvrID和实际消息内容"""
        # 检查是否是引用消息
        if not compress_content or str(sub_type) not in ["51", "57"]:
            return None, None

        try:
            import base64
            import re
            import xml.etree.ElementTree as ET

            # base64解码
            decoded = base64.b64decode(compress_content).decode('utf-8', errors='ignore')
            logger.debug(f"CompressContent解码结果: {decoded[:200]}...")

            # 提取被引用消息的服务器ID (MsgSvrID)
            # 方法1: 查找<svrid>标签
            svrid_matches = re.findall(r'<svrid>(\d+)</svrid>', decoded)
            if not svrid_matches:
                # 方法2: 查找长数字ID（15-20位）
                svrid_matches = re.findall(r'\b(\d{15,20})\b', decoded)
            quote_id = svrid_matches[0] if svrid_matches else None
            logger.debug(f"提取到被引用消息ID: {quote_id}")

            # 尝试解析XML获取实际消息内容
            actual_content = None
            try:
                # 方法1: 查找title标签中的CDATA内容
                title_match = re.search(r'<title[^>]*><!\[CDATA\[(.*?)\]\]></title>', decoded, re.DOTALL)
                if title_match:
                    actual_content = title_match.group(1).strip()
                    logger.debug(f"从title CDATA提取: {actual_content}")

                # 方法2: 查找没有CDATA的title标签
                if not actual_content:
                    title_match = re.search(r'<title[^>]*>(.*?)</title>', decoded, re.DOTALL)
                    if title_match:
                        content = title_match.group(1).strip()
                        # 清理HTML标签和特殊字符
                        content = re.sub(r'<[^>]+>', '', content)
                        content = re.sub(r'[^\w\s\u4e00-\u9fff]', '', content).strip()
                        if content and len(content) > 0:
                            actual_content = content
                            logger.debug(f"从title标签提取: {actual_content}")

                # 方法2.5: 特殊处理title标签中被截断的内容
                if not actual_content:
                    # 查找类似 <title>内容</q 这样被截断的标签
                    title_partial = re.search(r'<title[^>]*>([^<]+)</q', decoded)
                    if title_partial:
                        content = title_partial.group(1).strip()
                        if content and len(content) > 0:
                            actual_content = content
                            logger.debug(f"从截断title提取: {actual_content}")

                # 方法3: 查找content标签
                if not actual_content:
                    content_match = re.search(r'<content[^>]*><!\[CDATA\[(.*?)\]\]></content>', decoded, re.DOTALL)
                    if content_match:
                        actual_content = content_match.group(1).strip()

                # 方法4: 查找没有CDATA的content标签
                if not actual_content:
                    content_match = re.search(r'<content[^>]*>(.*?)</content>', decoded, re.DOTALL)
                    if content_match:
                        content = content_match.group(1).strip()
                        # 清理HTML标签
                        content = re.sub(r'<[^>]+>', '', content).strip()
                        if content:
                            actual_content = content

                # 方法5: 查找digest标签（摘要）
                if not actual_content:
                    digest_match = re.search(r'<digest[^>]*><!\[CDATA\[(.*?)\]\]></digest>', decoded, re.DOTALL)
                    if digest_match:
                        actual_content = digest_match.group(1).strip()

                # 方法6: 查找用户实际输入的回复内容
                if not actual_content:
                    # 在引用消息的XML中，用户输入的内容可能在不同的位置
                    # 尝试查找所有可能的文本内容


                    # 查找所有文本节点
                    all_text_patterns = [
                        r'<title[^>]*>([^<]+)</title>',
                        r'<content[^>]*>([^<]+)</content>',
                        r'<des[^>]*>([^<]+)</des>',
                        r'<digest[^>]*>([^<]+)</digest>',
                        r'CDATA\[([^\]]+)\]',
                        r'>([^<]{3,})</',  # 任何标签内的文本，至少3个字符
                    ]

                    potential_contents = []
                    for pattern in all_text_patterns:
                        matches = re.findall(pattern, decoded, re.IGNORECASE | re.DOTALL)
                        for match in matches:
                            match = match.strip()
                            if (match and
                                len(match) > 2 and
                                not match.startswith('<?xml') and
                                not match.startswith('<msg') and
                                not match.isdigit() and  # 排除纯数字
                                'wxid_' not in match):   # 排除微信ID
                                potential_contents.append(match)


                    # 选择最可能是用户输入的内容
                    if potential_contents:
                        # 过滤掉用户名、ID等不相关内容
                        filtered_contents = []
                        for content in potential_contents:
                            # 排除用户名、微信ID、消息ID等
                            if (not any(name in content for name in ['张Wonder', 'Wonder', '小张']) and
                                'wxid_' not in content and
                                not content.isdigit() and  # 排除纯数字
                                not re.match(r'^[0-9]+[A-Z][0-9]+$', content) and  # 排除消息ID格式
                                len(content) > 1 and
                                len(content) < 100):  # 排除过长的内容
                                filtered_contents.append(content)

                        # 如果有过滤后的内容，选择最长的；否则选择原始内容中最长的
                        if filtered_contents:
                            actual_content = max(filtered_contents, key=len)
                            logger.debug(f"从过滤内容中选择: {actual_content}")
                        else:
                            actual_content = max(potential_contents, key=len)
                            logger.debug(f"从原始内容中选择: {actual_content}")

            except Exception as e:
                logger.debug(f"解析引用消息内容失败: {e}")

            # 返回引用信息和实际内容
            quote_info = {"被引用消息ID": quote_id} if quote_id else None
            logger.debug(f"parse_quote_info返回: quote_info={quote_info}, actual_content='{actual_content}'")
            return quote_info, actual_content

        except Exception as e:
            return None, None

    def call_coze_api(self, message):
        """调用Coze API获取回复"""
        try:
            headers = {
                "Authorization": f"Bearer {COZE_TOKEN}",
                "Content-Type": "application/json"
            }

            data = {
                "bot_id": COZE_BOT_ID,
                "user": "wechat_user",
                "query": message,
                "stream": False
            }

            # 创建一个session并设置代理为None
            session = requests.Session()
            session.trust_env = False  # 忽略环境变量中的代理设置

            response = session.post(
                "https://api.coze.cn/open_api/v2/chat",
                headers=headers,
                json=data,
                verify=False,
                timeout=15,
                proxies={"http": None, "https": None}  # 明确禁用代理
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    # 检查messages是否在根级别
                    messages = result.get("messages", [])
                    if not messages and result.get("data"):
                        # 如果messages在data字段下
                        messages = result["data"].get("messages", [])

                    for msg in messages:
                        if msg.get("type") == "answer":
                            return msg.get("content", "")

                logger.warning(f"Coze API返回异常: {result}")
                return "抱歉，我现在无法回复您的消息。"
            else:
                logger.error(f"Coze API请求失败: {response.status_code}")
                return "抱歉，我现在无法回复您的消息。"

        except Exception as e:
            logger.error(f"调用Coze API异常: {e}")
            return "抱歉，我现在无法回复您的消息。"

    def clean_message_content(self, content, msg_type, sub_type):
        """清理和验证消息内容，过滤掉群聊ID等无效内容"""
        if not content:
            return ""

        try:
            # 检查是否是群聊ID格式 (数字@chatroom)
            if re.match(r'^\d+@chatroom$', content.strip()):
                logger.debug(f"检测到群聊ID格式内容，忽略: {content}")
                return "[系统消息]"

            # 检查是否是微信ID格式 (wxid_开头)
            if content.strip().startswith('wxid_'):
                logger.debug(f"检测到微信ID格式内容，忽略: {content}")
                return "[系统消息]"

            # 检查是否是纯数字ID
            if content.strip().isdigit() and len(content.strip()) > 10:
                logger.debug(f"检测到长数字ID，忽略: {content}")
                return "[系统消息]"

            # 对于消息类型49，使用专门的清理方法
            if str(msg_type) == "49":
                return self.clean_type49_content(content)

            # 对于其他消息类型，进行基本清理
            clean_content = content.strip()

            # 移除控制字符
            clean_content = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', clean_content)

            # 移除多余的空白字符
            clean_content = re.sub(r'\s+', ' ', clean_content).strip()

            return clean_content if clean_content else ""

        except Exception as e:
            logger.debug(f"清理消息内容失败: {e}")
            return content or ""

    def clean_type49_content(self, content):
        """清理消息类型49的StrContent，移除二进制数据，只保留可读文本"""
        try:
            if not content:
                return ""

            # 方法1：查找第一个控制字符的位置，截取之前的内容
            clean_content = ""
            for i, char in enumerate(content):
                char_code = ord(char)
                # 检查是否是控制字符（0-31，除了常见的空白字符）
                if char_code < 32 and char not in ['\n', '\r', '\t', ' ']:
                    # 遇到控制字符，截取到这里
                    clean_content = content[:i]
                    break
                # 检查是否是其他不可打印字符
                elif char_code > 126 and char_code < 160:
                    clean_content = content[:i]
                    break
            else:
                # 如果没有遇到控制字符，使用全部内容
                clean_content = content

            # 方法2：使用正则表达式进一步清理
            if clean_content:
                # 移除HTML实体
                clean_content = re.sub(r'&[a-zA-Z]+;', '', clean_content)
                # 移除末尾的特殊字符
                clean_content = re.sub(r'[/\\]+$', '', clean_content)
                clean_content = clean_content.strip()

            # 调试信息
            if clean_content != content:
                logger.debug(f"内容清理: '{content[:50]}...' -> '{clean_content}'")

            return clean_content if clean_content else ""

        except Exception as e:
            logger.debug(f"清理消息类型49内容失败: {e}")
            return ""

    def get_reply_context_content(self, quote_msg_id, current_local_id):
        """通过被引用消息ID和当前消息ID，查找用户实际的回复内容"""
        try:
            if not quote_msg_id or not current_local_id or not self.db_handle:
                return None

            # 查找被引用消息的localId
            sql = f"SELECT localId FROM MSG WHERE MsgSvrID = '{quote_msg_id}' LIMIT 1;"

            response = requests.post(
                f"{WECHAT_RPC_HOST}/api/db/execSql",
                headers={"X-WeChat-PID": WECHAT_PID},
                json={"dbHandle": self.db_handle, "sql": sql}
            )

            quote_local_id = None
            if response.status_code == 200:
                result = response.json()
                if result.get("Code") == 1 and result.get("Data") and len(result["Data"]) > 1:
                    quote_local_id = result["Data"][1][0]

            if not quote_local_id:
                return None

            # 查找引用消息之后、当前消息之前的最近一条用户消息
            sql = f"""
            SELECT StrContent FROM MSG
            WHERE localId > {quote_local_id}
            AND localId < {current_local_id}
            AND Type = 1
            AND IsSender = 1
            AND StrTalker LIKE '%@chatroom'
            ORDER BY localId ASC
            LIMIT 1;
            """

            response = requests.post(
                f"{WECHAT_RPC_HOST}/api/db/execSql",
                headers={"X-WeChat-PID": WECHAT_PID},
                json={"dbHandle": self.db_handle, "sql": sql}
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("Code") == 1 and result.get("Data") and len(result["Data"]) > 1:
                    content = result["Data"][1][0]
                    if content and len(content.strip()) > 0:
                        return content.strip()

            return None

        except Exception as e:
            logger.debug(f"获取回复上下文内容失败: {e}")
            return None

    def get_message_content_by_id(self, msg_id):
        """根据消息ID查找消息内容"""
        try:
            if not msg_id or not self.db_handle:
                return None

            # 查询消息内容，优先使用MsgSvrID匹配
            sql = f"SELECT StrContent FROM MSG WHERE MsgSvrID = '{msg_id}' LIMIT 1;"

            logger.debug(f"查询被引用消息SQL: {sql}")

            response = requests.post(
                f"{WECHAT_RPC_HOST}/api/db/execSql",
                headers={"X-WeChat-PID": WECHAT_PID},
                json={"dbHandle": self.db_handle, "sql": sql}
            )

            if response.status_code == 200:
                result = response.json()
                logger.debug(f"查询结果: {result}")
                if result.get("Code") == 1 and result.get("Data") and len(result["Data"]) > 1:
                    # 第一行是列名，第二行才是数据
                    content = result["Data"][1][0]
                    logger.debug(f"找到被引用消息内容: '{content}'")
                    return content if content else ""
                else:
                    logger.debug(f"查询失败或无数据: {result}")
            else:
                logger.debug(f"查询请求失败: {response.status_code}")

            # 如果MsgSvrID查找失败，尝试使用localId查找（如果msg_id是local_格式）
            if str(msg_id).startswith("local_"):
                local_id = str(msg_id).replace("local_", "")
                sql = f"SELECT StrContent FROM MSG WHERE localId = {local_id} LIMIT 1;"

                response = requests.post(
                    f"{WECHAT_RPC_HOST}/api/db/execSql",
                    headers={"X-WeChat-PID": WECHAT_PID},
                    json={"dbHandle": self.db_handle, "sql": sql}
                )

                if response.status_code == 200:
                    result = response.json()
                    if result.get("Code") == 1 and result.get("Data") and len(result["Data"]) > 1:
                        # 第一行是列名，第二行才是数据
                        content = result["Data"][1][0]
                        return content if content else ""

            return None

        except Exception as e:
            logger.debug(f"根据ID查找消息内容失败: {e}")
            return None

    def find_user_reply_after_quote(self, quote_local_id):
        """查找引用消息后的用户回复内容"""
        try:
            if not quote_local_id or not self.db_handle:
                return None

            # 查找引用消息前后的用户消息（消息类型1）
            # 先查找引用消息之前的最近一条用户消息
            sql = f"""
            SELECT StrContent FROM MSG
            WHERE localId < {quote_local_id}
            AND Type = 1
            AND IsSender = 1
            ORDER BY localId DESC
            LIMIT 1;
            """

            logger.debug(f"查找用户回复SQL: {sql}")

            response = requests.post(
                f"{WECHAT_RPC_HOST}/api/db/execSql",
                headers={"X-WeChat-PID": WECHAT_PID},
                json={"dbHandle": self.db_handle, "sql": sql}
            )

            if response.status_code == 200:
                result = response.json()
                logger.debug(f"用户回复查询结果: {result}")
                if result.get("Code") == 1 and result.get("Data") and len(result["Data"]) > 1:
                    # 第一行是列名，第二行才是数据
                    content = result["Data"][1][0]
                    logger.debug(f"找到用户回复内容: '{content}'")
                    return content if content else None

            return None

        except Exception as e:
            logger.debug(f"查找用户回复失败: {e}")
            return None

    def extract_sender_wxid(self, bytes_extra):
        """从BytesExtra中提取发送者微信ID"""
        if not bytes_extra:
            return None

        try:
            bytes_str = str(bytes_extra)

            # 尝试base64解码
            try:
                decoded = base64.b64decode(bytes_str).decode('utf-8', errors='ignore')
                wxid_pattern = r'wxid_[a-zA-Z0-9]+'
                match = re.search(wxid_pattern, decoded)
                if match:
                    return match.group(0)
            except:
                pass

            # 直接查找
            wxid_pattern = r'wxid_[a-zA-Z0-9]+'
            match = re.search(wxid_pattern, bytes_str)
            if match:
                return match.group(0)

            return None
        except:
            return None

    def process_message(self, msg_data):
        """处理单条消息"""
        try:
            # 解析消息数据 - 按照SQL查询顺序
            local_id = msg_data[0]
            talker_id = msg_data[1]  # TalkerId
            create_time = msg_data[2]
            str_talker = msg_data[3]  # 群ID
            str_content = msg_data[4]  # 消息内容
            is_sender = msg_data[5]  # 是否自己发送
            msg_type = msg_data[6]  # 消息类型
            sub_type = msg_data[7]  # 子类型
            bytes_extra = msg_data[8]  # 发送者信息
            compress_content = msg_data[9]  # 压缩内容（引用信息）
            msg_svr_id = msg_data[10]  # 服务器消息ID

            # 跳过自己发送的消息
            if is_sender == 1:
                return

            # 提取发送者微信ID
            sender_wxid = self.extract_sender_wxid(bytes_extra)
            if not sender_wxid:
                return

            # 获取发送者昵称
            sender_name = self.get_contact_name(sender_wxid)

            # 获取群名称
            group_name = self.get_contact_name(str_talker)

            # 格式化时间
            try:
                # 确保create_time是数字类型
                if isinstance(create_time, str):
                    create_time = int(create_time)
                formatted_time = datetime.datetime.fromtimestamp(create_time).strftime('%Y-%m-%d %H:%M:%S')
            except (ValueError, TypeError, OSError):
                formatted_time = "未知时间"

            # 处理消息ID - 优先使用MsgSvrID
            if msg_svr_id and str(msg_svr_id) != "0":
                message_id = str(msg_svr_id)
            else:
                message_id = f"local_{local_id}"

            # 处理消息内容解析
            actual_content = self.clean_message_content(str_content, msg_type, sub_type)
            quote_info = None

            # 如果是引用消息，解析引用信息
            if str(msg_type) == "49" and str(sub_type) in ["51", "57"]:
                quote_info, parsed_content = self.parse_quote_info(compress_content, sub_type)

                # 对于消息类型49，尝试多种方法获取用户实际输入的回复内容
                actual_content = ""

                # 方法1: 使用从CompressContent解析出的内容
                if parsed_content and parsed_content != "None" and len(parsed_content.strip()) > 0:
                    actual_content = parsed_content
                    logger.debug(f"使用CompressContent解析的内容: {actual_content}")

                # 方法2: 如果CompressContent解析失败，尝试从StrContent提取
                if not actual_content and str_content:
                    # 清理StrContent中的特殊字符和引用信息
                    cleaned = self.clean_type49_content(str_content)
                    if cleaned and len(cleaned.strip()) > 0:
                        actual_content = cleaned
                        logger.debug(f"使用StrContent清理的内容: {actual_content}")

                # 方法3: 尝试从BytesExtra字段提取
                if not actual_content and bytes_extra:
                    try:
                        # BytesExtra可能包含用户输入的内容
                        bytes_content = bytes_extra.decode('utf-8', errors='ignore')
                        if bytes_content and len(bytes_content.strip()) > 0:
                            cleaned_bytes = self.clean_type49_content(bytes_content)
                            if cleaned_bytes and cleaned_bytes != bytes_content:
                                actual_content = cleaned_bytes
                                logger.debug(f"从BytesExtra提取到内容: {actual_content}")
                    except Exception as e:
                        logger.debug(f"解析BytesExtra失败: {e}")

                # 方法4: 尝试通过被引用消息ID查找上下文
                if not actual_content and quote_info and quote_info.get("被引用消息ID"):
                    try:
                        context_content = self.get_reply_context_content(quote_info["被引用消息ID"], local_id)
                        if context_content:
                            actual_content = context_content
                            logger.debug(f"从上下文提取到内容: {actual_content}")
                    except Exception as e:
                        logger.debug(f"获取上下文内容失败: {e}")

                # 方法5: 如果都没有内容，尝试直接使用StrContent
                if not actual_content:
                    if str_content and len(str_content.strip()) > 0:
                        # 先尝试清理StrContent
                        cleaned_str = self.clean_type49_content(str_content)
                        if cleaned_str and len(cleaned_str) > 0:
                            actual_content = cleaned_str
                        else:
                            # 如果清理后没有内容，使用原始内容
                            actual_content = str_content.strip()
                    else:
                        actual_content = "[引用回复]"  # 默认显示
                        logger.debug(f"引用回复内容解析失败，使用默认显示，消息ID: {message_id}")

            # 构建消息对象 - 只包含需要的字段
            message_obj = {
                "发送人": sender_name,
                "发送时间": formatted_time,
                "群聊名称": group_name,
                "消息内容": actual_content,
                "消息类型": str(msg_type),
                "消息ID": message_id
            }

            # 添加引用信息
            if quote_info:
                message_obj["引用信息"] = quote_info

            # 为内部处理保留额外字段
            message_obj["群ID"] = str_talker
            message_obj["发送者ID"] = sender_wxid

            # 构建输出对象 - 只包含需要显示的字段
            output_obj = {
                "发送人": message_obj["发送人"],
                "发送时间": message_obj["发送时间"],
                "群聊名称": message_obj["群聊名称"],
                "消息内容": message_obj["消息内容"],
                "消息类型": message_obj["消息类型"],
                "消息ID": message_obj["消息ID"]
            }

            # 如果有引用信息，直接添加被引用消息ID字段
            if "引用信息" in message_obj and message_obj["引用信息"]:
                quote_info = message_obj["引用信息"]
                if "被引用消息ID" in quote_info:
                    output_obj["被引用消息ID"] = quote_info["被引用消息ID"]

            # 输出消息JSON
            print(json.dumps(output_obj, ensure_ascii=False, indent=2))

            # 上传到飞书多维表格
            try:
                upload_success = upload_message_to_feishu(output_obj)
                if not upload_success:
                    logger.warning("飞书上传失败，但程序继续运行")
            except Exception as e:
                logger.error(f"飞书上传异常: {e}")

            # 如果是测试群或者包含特定触发词，进行回复
            if self.should_reply(message_obj):
                self.handle_reply(message_obj)

        except Exception as e:
            logger.error(f"处理消息异常: {e}")

    def should_reply(self, message_obj):
        """判断是否应该回复消息"""
        # 只有当消息内容包含特定触发词时才回复
        content = message_obj.get("消息内容", "")

        # 只有当消息内容包含特定触发词时才回复
        if "@我来收集总结你们的聊天(bot)" in content:
            return True

        return False

    def handle_reply(self, message_obj):
        """处理回复逻辑"""
        content = message_obj.get("消息内容", "")
        group_id = message_obj.get("群ID", "")
        sender_name = message_obj.get("发送人", "")
        group_name = message_obj.get("群聊名称", "")



        # 如果消息为空，使用默认回复
        if not content:
            reply = f"你好 {sender_name}，我是小夕智能助手！"
        else:
            # 预处理消息内容：移除@机器人的部分，只保留实际消息内容
            processed_content = self.preprocess_message(content)

            # 调用Coze API获取智能回复
            reply = self.call_coze_api(processed_content)

        # 发送回复
        self.send_wechat_message(group_id, reply)

    def monitor_messages(self):
        """监控新消息"""
        try:
            sql = f"""
            SELECT localId, TalkerId, CreateTime, StrTalker, StrContent, IsSender, Type, SubType, BytesExtra, CompressContent, MsgSvrID
            FROM MSG
            WHERE localId > {self.last_msg_id}
            AND StrTalker LIKE '%@chatroom'
            ORDER BY localId ASC;
            """

            response = requests.post(
                f"{WECHAT_RPC_HOST}/api/db/execSql",
                headers={"X-WeChat-PID": WECHAT_PID},
                json={"dbHandle": self.db_handle, "sql": sql}
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("Code") == 1 and result.get("Data"):
                    # 如果是第一次运行，跳过所有历史消息，只更新last_msg_id
                    if self.is_first_run:
                        # 只更新last_msg_id到最新消息，不处理消息内容
                        if result["Data"]:
                            self.last_msg_id = result["Data"][-1][0]  # 取最后一条消息的ID
                        self.is_first_run = False
                    else:
                        # 正常处理新消息
                        for msg_data in result["Data"]:
                            self.process_message(msg_data)
                            # 更新最后处理的消息ID
                            self.last_msg_id = msg_data[0]

        except Exception as e:
            logger.error(f"监控消息异常: {e}")

    def start(self):
        """启动监控"""
        print("🚀 正在启动微信消息监控系统...")
        print(f"📡 微信RPC地址: {WECHAT_RPC_HOST}")
        print(f"🔢 微信进程ID: {WECHAT_PID}")

        # 初始化数据库连接
        print("📊 正在连接微信数据库...")
        if not self.get_db_handle():
            print("❌ 无法连接到微信数据库，请检查:")
            print("   1. 微信RPC服务是否启动 (http://127.0.0.1:50007)")
            print("   2. 微信进程ID是否正确")
            print("   3. 微信是否已登录")
            return
        print("✅ 数据库连接成功")

        # 加载联系人
        print("👥 正在加载联系人信息...")
        self.load_contacts()
        print(f"✅ 已加载 {len(self.contacts_cache)} 个联系人")

        # 初始化监控起点
        print("🎯 正在初始化消息监控起点...")
        self.init_monitoring()
        print(f"✅ 监控起点设置完成，当前消息ID: {self.last_msg_id}")

        print("🔄 开始监控新消息...")
        print("💡 程序正在运行中，按 Ctrl+C 停止")
        print("=" * 50)

        try:
            while True:
                self.monitor_messages()
                time.sleep(3)  # 每3秒检查一次新消息

        except KeyboardInterrupt:
            print("\n⏹️  用户手动停止程序")
        except Exception as e:
            logger.error(f"监控循环异常: {e}")
            print(f"❌ 程序异常: {e}")

def main():
    """主函数"""
    try:
        bot = WeChatCozeBot()
        bot.start()
    except KeyboardInterrupt:
        pass
    except Exception as e:
        logger.error(f"程序异常: {e}")

if __name__ == "__main__":
    main()